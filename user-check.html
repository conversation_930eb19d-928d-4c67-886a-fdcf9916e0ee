<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pyxi • New or Existing?</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=Work+Sans:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <script src="./js/config.js"></script>
  </head>
  <body
    class="font-sans bg-[#FFF9EC] min-h-screen flex items-center justify-center py-10 px-4"
  >
    <div
      class="w-full max-w-lg bg-white rounded-2xl shadow-xl p-6 md:p-8 border border-gray-100"
    >
      <div class="flex flex-col items-center">
        <img src="./images/pyxi-logo.png" alt="Pyxi" class="h-8 w-auto" />
      </div>

      <h1
        class="text-center mt-6 text-2xl md:text-3xl font-semibold text-gray-900"
      >
        Are you a new user or<br />an existing one?
      </h1>

      <form class="mt-6" onsubmit="event.preventDefault(); handleSubmit();">
        <label class="block text-sm font-medium text-gray-700">Email</label>
        <input
          id="email"
          type="email"
          required
          placeholder="<EMAIL>"
          class="mt-2 w-full px-3 py-2 border rounded-lg"
        />
        <button
          type="submit"
          class="w-full mt-4 bg-[#F09E5B] text-white font-semibold py-3 rounded-lg hover:opacity-90"
        >
          Submit
        </button>
      </form>

      <button
        id="newUserBtn"
        class="w-full mt-3 bg-[#FFF4E8] text-[#F09E5B] font-medium py-3 rounded-lg hover:bg-gray-200"
      >
        I'm New User
      </button>
    </div>

    <script>
      async function handleExistingUser(guestUid) {
        try {
          // Check if PyxiConfig is available
          if (typeof PyxiConfig === 'undefined') {
            console.error('PyxiConfig is not defined. Make sure config.js is loaded properly.');
            showErrorMessage("Configuration error. Please refresh the page and try again.");
            return;
          }

          // Get order_id from URL parameters
          const orderId = new URLSearchParams(window.location.search).get('order_id');

          if (!orderId) {
            showErrorMessage("Missing order information. Please try again.");
            return;
          }

          // Call the add plus one API
          const response = await fetch(
            `${PyxiConfig.API.BASE_URL}/orders/pyxi-select/add-plus-one/${orderId}?guest_uid=${guestUid}`,
            {
              method: 'POST',
              headers: {
                'accept': 'application/json'
              },
              body: ''
            }
          );

          if (response.ok) {
            // Show success message
            showSuccessMessage();
          } else {
            showErrorMessage("There was a problem confirming your seat. Please try again.");
          }
        } catch (error) {
          console.error('Error calling add plus one API:', error);
          showErrorMessage("There was a problem confirming your seat. Please try again.");
        }
      }

      function showSuccessMessage() {
        // Hide the form
        document.querySelector('form').style.display = 'none';
        document.getElementById('newUserBtn').style.display = 'none';

        // Create and show success message
        const container = document.querySelector('.w-full.max-w-lg');
        const successDiv = document.createElement('div');
        successDiv.className = 'text-center mt-6';
        successDiv.innerHTML = `
          <div class="bg-green-50 border border-green-200 rounded-lg p-6">
            <div class="flex justify-center mb-4">
              <svg class="w-12 h-12 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h2 class="text-xl font-semibold text-green-800 mb-2">Seat Confirmed!</h2>
            <p class="text-green-700">
              You have successfully confirmed your seat. We've sent a confirmation email to you - please check your inbox.
            </p>
          </div>
        `;
        container.appendChild(successDiv);
      }

      function showErrorMessage(message) {
        // Remove any previous error
        let errorMsg = document.getElementById("user-error-msg");
        if (errorMsg) errorMsg.remove();

        // Show error message
        let error = document.createElement("div");
        error.id = "user-error-msg";
        error.className = "text-red-500 mt-4 text-sm text-center";
        error.textContent = message;
        document.querySelector("form").appendChild(error);

        // Re-enable submit button
        const submitBtn = document.querySelector('button[type="submit"]');
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Submit';
      }

      async function handleSubmit() {
        // Check if PyxiConfig is available
        if (typeof PyxiConfig === 'undefined') {
          console.error('PyxiConfig is not defined. Make sure config.js is loaded properly.');
          showErrorMessage("Configuration error. Please refresh the page and try again.");
          return;
        }

        const emailInput = document.getElementById("email");
        const submitBtn = document.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;

        // Show loader and disable button
        submitBtn.disabled = true;
        submitBtn.innerHTML =
          '<svg class="animate-spin h-5 w-5 mr-2 inline-block text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>Loading...';

        // Remove any previous error
        let errorMsg = document.getElementById("user-error-msg");
        if (errorMsg) errorMsg.remove();

        try {
          const email = encodeURIComponent(emailInput.value.trim());
          const response = await fetch(
            `${PyxiConfig.API.BASE_URL}${PyxiConfig.ENDPOINTS.CHECK_EMAIL_EXISTS}?email=${email}`,
            {
              method: "GET",
              headers: { accept: "application/json" },
            }
          );

          if (response.ok) {
            const data = await response.json();
            if (data.exists) {
              const uid = data.uid;
              // User exists - call the add plus one API
              await handleExistingUser(uid);
            } else {
              // User doesn't exist - navigate to signup quiz
              const orderId = new URLSearchParams(window.location.search).get('order_id');
              const quizUrl = orderId ? `${PyxiConfig.API.SIGNUP_URL}/1-plus-quiz?order_id=${orderId}` : `${PyxiConfig.API.SIGNUP_URL}/1-plus-quiz`;
              window.location.href = quizUrl;
            }
          } else if (response.status === 404) {
            // User doesn't exist - navigate to signup quiz
            const orderId = new URLSearchParams(window.location.search).get('order_id');
            const quizUrl = orderId ? `${PyxiConfig.API.SIGNUP_URL}/1-plus-quiz?order_id=${orderId}` : `${PyxiConfig.API.SIGNUP_URL}/1-plus-quiz`;
            window.location.href = quizUrl;
          } else {
            // Show error message for other errors
            showErrorMessage("There was a problem checking your email. Please try again.");
          }
        } catch (err) {
          // Show error message for network errors
          showErrorMessage("There was a problem checking your email. Please try again.");
        } finally {
          // Re-enable button
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalBtnText;
        }
      }

      document.getElementById("newUserBtn").addEventListener("click", () => {
        // Check if PyxiConfig is available
        if (typeof PyxiConfig === 'undefined') {
          console.error('PyxiConfig is not defined. Make sure config.js is loaded properly.');
          showErrorMessage("Configuration error. Please refresh the page and try again.");
          return;
        }

        // For new users, directly navigate to signup quiz
        const orderId = new URLSearchParams(window.location.search).get('order_id');
        const quizUrl = orderId ? `${PyxiConfig.API.SIGNUP_URL}/1-plus-quiz?order_id=${orderId}` : `${PyxiConfig.API.SIGNUP_URL}/1-plus-quiz`;
        window.location.href = quizUrl;
      });
    </script>
  </body>
</html>
