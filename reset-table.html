<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="./images/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="./images/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="./images/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="./images/favicon/site.webmanifest" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>The Reset Table | Pyxi</title>
    <meta
      name="description"
      content="The Reset Table - Intimate dinners designed to spark meaningful conversations. Join Pyxi to build genuine friendships through curated social experiences."
    />
    <meta property="og:title" content="The Reset Table | Pyxi" />
    <meta
      property="og:description"
      content="Intimate dinners designed to spark meaningful conversations. Build genuine friendships through curated social experiences."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://pyxi.com/reset-table.html" />
    <meta
      property="og:image"
      content="https://pyxi.com/images/pyxi-social-preview.jpg"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=Work+Sans:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="./css/styles.css" />
    <link rel="stylesheet" href="./css/mobile-fixes.css" />
    <link rel="stylesheet" href="./css/pyxi-select-mobile.css" />
    <link rel="stylesheet" href="./css/custom.css" />
    <script src="./js/config.js"></script>
    <script src="./js/navbar.js"></script>
    <script src="./js/pyxi.js"></script>
  </head>

  <body class="font-sans antialiased bg-[#fcf9ed] text-gray-800">
    <!-- Navigation -->
    <nav
      id="navbar"
      class="fixed top-0 w-full z-50 transition-all duration-300"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
          <!-- Logo -->
          <div class="flex-shrink-0">
            <img
              src="./images/pyxi-logo.png"
              alt="Pyxi"
              class="h-8 w-auto cursor-pointer"
              onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
            />
          </div>

          <!-- Desktop CTA -->
          

          <!-- Mobile menu button -->
          <button id="mobile-menu-btn" class="lg:hidden text-black p-2">
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobile-menu"
        class="lg:hidden hidden bg-gray-900/95 backdrop-blur-sm border-t border-gray-800 w-full"
      >
        <div class="px-4 py-6 space-y-4">
          <button
            class="w-full bg-pyxi-red text-white px-6 py-3 font-semibold rounded-full hover:bg-red-700 transition-colors mt-4"
            onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')"
          >
            Join Your First Dinner
          </button>
          <a
            href="#returning-diner"
            onclick="scrollToReturningDiner(); closeMobileMenu(); return false;"
            class="block w-full border-2 border-pyxi-red text-pyxi-red px-6 py-3 font-semibold rounded-full hover:bg-pyxi-red hover:text-white transition-colors text-center"
          >
            Returning Guest
          </a>
        </div>
      </div>
    </nav>

    <!-- Main Content Section -->
    <section class="pt-32 pb-16">
      <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Content will go here -->
        <h1 class="font-marker text-6xl sm:text-6xl text-gray-800 mb-8">
          The Reset Table 🍽️
        </h1>
        <p class="text-xl font-bold text-pyxi-red mb-8">
          <span class="bg-yellow-100 px-3 py-1 rounded-full">TUESDAYS</span>
          &
          <span class="bg-yellow-100 px-3 py-1 rounded-full">THURSDAYS</span>
        </p>
        <h2 class="font-marker text-4xl sm:text-5xl text-gray-800 mb-8">
          Dinner with a matched group of strangers
        </h2>
        <p class="text-lg text-gray-600 mb-8">
          Share a meaningful dinner with 5 like-minded guests — thoughtfully
          matched by our compatibility algorithm. Real conversations. Genuine
          connections. One unforgettable evening.
        </p>
        <button
          onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')"
          data-cta="personality-quiz"
          class="bg-pyxi-red text-white px-10 py-5 text-lg font-semibold rounded-full hover:bg-red-700 transition-all shadow-lg mb-[10px]"
        >
          Take the Personality Quiz 🧠
        </button>
        <button
          onclick="scrollToReturningDiner(); closeMobileMenu(); return false;"
          class="border-2 border-pyxi-red text-pyxi-red px-10 py-5 text-lg font-semibold rounded-full hover:bg-pyxi-red hover:text-white transition-all ml-[10px] mb-[10px]"
          type="button"
        >
          Returning Guest?
        </button>
      </div>
    </section>

    <!-- Featured In Section -->
    <section class="bg-[rgb(240,158,91)] py-12">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
          <h2 class="font-marker text-2xl sm:text-3xl text-gray-800 mb-4">
            As Featured In ✨
          </h2>
        </div>
        <div class="relative overflow-hidden">
          <div class="flex animate-scroll">
            <div class="flex items-center space-x-16 flex-shrink-0">
              <img
                src="./images/forbes_logo.png"
                alt="Forbes"
                class="h-8 w-auto object-contain opacity-80"
              />
              <img
                src="./images/BBC_logo.svg"
                alt="BBC"
                class="h-8 w-auto object-contain opacity-80"
              />
              <img
                src="./images/bss_logo.png"
                alt="British Style Society"
                class="h-6 w-auto object-contain opacity-80"
              />
              <img
                src="./images/evening_standard_logo.png"
                alt="Evening Standard"
                class="h-8 w-auto object-contain opacity-80"
              />
            </div>
            <div class="w-16 flex-shrink-0"></div>
            <div class="flex items-center space-x-16 flex-shrink-0">
              <img
                src="./images/forbes_logo.png"
                alt="Forbes"
                class="h-8 w-auto object-contain opacity-80"
              />
              <img
                src="./images/BBC_logo.svg"
                alt="BBC"
                class="h-8 w-auto object-contain opacity-80"
              />
              <img
                src="./images/bss_logo.png"
                alt="British Style Society"
                class="h-6 w-auto object-contain opacity-80"
              />
              <img
                src="./images/evening_standard_logo.png"
                alt="Evening Standard"
                class="h-8 w-auto object-contain opacity-80"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="bg-gray-100 py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2
            class="font-marker text-3xl sm:text-4xl lg:text-5xl text-gray-800 mb-6"
          >
            How <span class="text-pyxi-red">It Works</span> 🔍
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Four simple steps to building meaningful connections
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-8">
          <div
            class="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all"
          >
            <div
              class="w-20 h-20 bg-pyxi-red rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <span class="text-white text-2xl font-bold">1</span>
            </div>
            <h3 class="font-marker text-2xl text-gray-800 mb-4">
              Take Our Personality Test 📝
            </h3>
            <p class="text-gray-600 leading-relaxed">
              Answer a few simple questions about how you connect with others.
              Our algorithm uses this to find your ideal group.
            </p>
            <button
              onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')"
              class="mt-6 bg-pyxi-red text-white px-6 py-3 text-sm font-semibold rounded-full hover:bg-red-700 transition-all"
            >
              Take the Quiz Now
            </button>
          </div>

          <div
            class="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all"
          >
            <div
              class="w-20 h-20 bg-pyxi-red rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <span class="text-white text-2xl font-bold">2</span>
            </div>
            <h3 class="font-marker text-2xl text-gray-800 mb-4">
              Get Matched 🔄
            </h3>
            <p class="text-gray-600 leading-relaxed">
              We pair you with up to five compatible strangers who share your vibe -
              people you're likely to click with over good conversation.
            </p>
          </div>

          <div
            class="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all"
          >
            <div
              class="w-20 h-20 bg-pyxi-red rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <span class="text-white text-2xl font-bold">3</span>
            </div>
            <h3 class="font-marker text-2xl text-gray-800 mb-4">
              We organize the details 📅
            </h3>
            <p class="text-gray-600 leading-relaxed">
              We book your dinner, share your restaurant details, and give you a
              quick intro to the evening so you’ll know what to expect.
            </p>
          </div>

          <div
            class="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all"
          >
            <div
              class="w-20 h-20 bg-pyxi-red rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <span class="text-white text-2xl font-bold">4</span>
            </div>
            <h3 class="font-marker text-2xl text-gray-800 mb-4">
              Show up and Connect 🎉
            </h3>
            <p class="text-gray-600 leading-relaxed">
              Show up and enjoy! Share a meal, meet new people, and enjoy
              meaningful conversation in a relaxed setting.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="bg-[#fcf9ed] py-16 sm:py-24">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2
            class="font-marker text-3xl sm:text-4xl lg:text-5xl text-gray-800 mb-6"
          >
            Real Stories, Real <span class="text-pyxi-red">Connections</span> 💬
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Hear from our members
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Testimonial 1 -->
          <div
            class="bg-white rounded-3xl p-8 shadow-xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2"
          >
            <div class="flex items-center mb-6">
              <picture>
                <source srcset="./images/james.jpg" type="image/webp" />
                <img
                  src="./images/james.jpg"
                  alt="James"
                  class="w-40 h-40 rounded-full object-cover mr-4"
                  loading="lazy"
                />
              </picture>
              <div>
                <h4 class="text-gray-800 font-semibold text-lg">James</h4>
                <p class="text-gray-500 text-sm">Software Engineer</p>
              </div>
            </div>
            <blockquote class="text-gray-600 leading-relaxed mb-4">
              "Pyxi set us up in an interesting restaurant very close to where I
              live making it easy to get to. Not only that It was a unique place
              where I was able to try something new which I really enjoyed. The
              other people's careers and live genuinely interested me and we
              have been able to keep in touch after. I look forward to using
              Pyxi again."
            </blockquote>
            <div class="flex text-pyxi-red text-sm">⭐⭐⭐⭐⭐</div>
          </div>

          <!-- Testimonial 2 -->
          <div
            class="bg-white rounded-3xl p-8 shadow-xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2"
          >
            <div class="flex items-center mb-6">
              <picture>
                <source srcset="./images/serena.jpg" type="image/webp" />
                <img
                  src="./images/serena.jpg"
                  alt="Serena"
                  class="w-40 h-40 rounded-full object-cover mr-4"
                  loading="lazy"
                />
              </picture>
              <div>
                <h4 class="text-gray-800 font-semibold text-lg">Serena</h4>
                <p class="text-gray-500 text-sm">Sustainability Director</p>
              </div>
            </div>
            <blockquote class="text-gray-600 leading-relaxed mb-4">
              "Working remotely can get really isolating — some days I barely
              speak to anyone outside of Zoom. Pyxi was such a refreshing
              change. The dinner gave me a chance to meet interesting people in
              a relaxed setting, without the awkwardness of traditional
              networking. I left feeling energised and more connected"
            </blockquote>
            <div class="flex text-pyxi-red text-sm">⭐⭐⭐⭐⭐</div>
          </div>

          <!-- Testimonial 3 -->
          <div
            class="bg-white rounded-3xl p-8 shadow-xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2"
          >
            <div class="flex items-center mb-6">
              <picture>
                <source srcset="./images/natalie.jpg" type="image/webp" />
                <img
                  src="./images/natalie.jpg"
                  alt="Natalie"
                  class="w-40 h-40 rounded-full object-cover mr-4"
                  loading="lazy"
                />
              </picture>
              <div>
                <h4 class="text-gray-800 font-semibold text-lg">Natalie</h4>
                <p class="text-gray-500 text-sm">Lawyer</p>
              </div>
            </div>
            <blockquote class="text-gray-600 leading-relaxed mb-4">
              "Pyxi dinners are like networking but better. You have deep
              meaningful conversations with interesting people but in a very
              natural way over a nice meal and wine"
            </blockquote>
            <div class="flex text-pyxi-red text-sm">⭐⭐⭐⭐⭐</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Personality Quiz Section -->
    <section
      class="bg-gradient-to-br from-purple-600 to-indigo-700 py-16 sm:py-20 relative overflow-hidden"
    >
      <div class="absolute inset-0 bg-black bg-opacity-10"></div>
      <div
        class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
      >
        <h2 class="font-marker text-4xl sm:text-5xl text-white mb-8">
          We like to think we're logical. But most decisions start in the 🧠 and
          land in the gut.
          <br /><span class="text-yellow-300">Which one are you?</span>
        </h2>

        <div class="flex flex-col sm:flex-row gap-6 justify-center mt-12">
          <button
            onclick="window.open('https://signup.pyxi.ai/quiz?type=heart', '_blank')"
            class="bg-pink-500 text-white px-10 py-6 text-xl font-semibold rounded-2xl hover:bg-pink-600 transition-all shadow-lg transform hover:scale-105"
          >
            ❤️ Feelings First
          </button>

          <button
            onclick="window.open('https://signup.pyxi.ai/quiz?type=brain', '_blank')"
            class="bg-blue-500 text-white px-10 py-6 text-xl font-semibold rounded-2xl hover:bg-blue-600 transition-all shadow-lg transform hover:scale-105"
          >
            🧠 Brain First
          </button>
        </div>

        <p class="text-white text-lg mt-8">
          Take our personality quiz and find your perfect dinner match! ✨
        </p>
      </div>
    </section>

    <!-- Meet Our Members Section -->
    <section class="bg-[#fcf9ed] py-16 sm:py-24">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2
            class="font-marker text-3xl sm:text-4xl lg:text-5xl text-gray-800 mb-6"
          >
            Meet Our <span class="text-pyxi-red">Members</span> 👋
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Extraordinary moments ahead
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Member 1 -->
          <div
            class="bg-white rounded-3xl p-8 shadow-xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2"
          >
            <img
              src="./images/emma.jpg"
              alt="Emma"
              class="w-full h-64 object-cover rounded-xl mb-6"
              loading="lazy"
            />
            <h3 class="font-marker text-2xl text-gray-800 mb-2">Emma, 27</h3>
            <p class="text-gray-600 font-semibold mb-2">UX Designer</p>
            <p class="text-gray-500">Once lived in a treehouse in Japan.</p>
          </div>

          <!-- Member 2 -->
          <div
            class="bg-white rounded-3xl p-8 shadow-xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2"
          >
            <img
              src="./images/michael.jpg"
              alt="Michael"
              class="w-full h-64 object-cover rounded-xl mb-6"
              loading="lazy"
            />
            <h3 class="font-marker text-2xl text-gray-800 mb-2">Michael, 32</h3>
            <p class="text-gray-600 font-semibold mb-2">Policy Analyst</p>
            <p class="text-gray-500">Biked across the Balkans with his dog.</p>
          </div>

          <!-- Member 3 -->
          <div
            class="bg-white rounded-3xl p-8 shadow-xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2"
          >
            <img
              src="./images/sofia.jpg"
              alt="Sofia"
              class="w-full h-64 object-cover rounded-xl mb-6"
              loading="lazy"
            />
            <h3 class="font-marker text-2xl text-gray-800 mb-2">Sofia, 29</h3>
            <p class="text-gray-600 font-semibold mb-2">Urban Planner</p>
            <p class="text-gray-500">
              Hosts a secret supper club in her attic.
            </p>
          </div>
        </div>
        <div class="text-center mt-12">
          <button
            onclick="window.open('https://signup.pyxi.ai/quiz?type=heart', '_blank')"
            class="bg-gradient-to-br from-purple-600 to-indigo-700 text-white w-full sm:w-auto px-6 sm:px-10 py-6 text-xl font-semibold rounded-2xl hover:bg-pink-600 transition-all shadow-lg transform hover:scale-105"
          >
            Discover Your Group
          </button>
        </div>
      </div>
    </section>

    <!-- Returning Diner Section -->
    <section id="returning-diner" class="bg-gray-900 py-20">
      <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="font-marker text-3xl sm:text-4xl text-white mb-6">
          Already Attended a Dinner with us?
        </h2>
        <p class="text-lg text-gray-300 mb-6">Enter your email below.</p>
        <form
          id="returning-diner-form"
          class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6"
        >
          <input
            type="email"
            id="returning-diner-email"
            required
            placeholder="Your email"
            class="px-4 py-3 rounded-full bg-gray-800 text-white border border-gray-600 focus:outline-none focus:ring-2 focus:ring-pyxi-red w-full sm:w-64"
          />
          <button
            type="submit"
            class="bg-pyxi-red text-white px-8 py-3 font-semibold rounded-full hover:bg-red-700 transition-colors"
          >
            Submit
          </button>
        </form>
        <div id="diner-dates" class="hidden mt-8">
          <h3 class="font-marker text-2xl text-white mb-4">
            Choose your next Dinner Date
          </h3>
          <div
            id="diner-dates-list"
            class="flex flex-wrap gap-4 justify-center"
          ></div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black border-t border-gray-800 py-16 sm:py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="lg:col-span-1">
            <img
              src="./images/pyxi-logo.png"
              alt="Pyxi"
              class="h-10 w-auto mb-6 filter brightness-0 invert"
            />
            <p class="text-gray-300 leading-relaxed mb-6">
              Building meaningful connections through curated experiences.
            </p>
            <div class="flex flex-col gap-3">
              <a
                href="https://play.google.com/store/apps/details?id=com.pyxida.pyxida&pcampaignid=web_share"
                class="inline-block hover:scale-105 transition-transform"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
                  alt="Get it on Google Play"
                  class="h-12 w-auto"
                />
              </a>
              <a
                href="https://apps.apple.com/za/app/pyxi/id1663673322"
                class="inline-block hover:scale-105 transition-transform"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/3/3c/Download_on_the_App_Store_Badge.svg"
                  alt="Download on the App Store"
                  class="h-12 w-auto"
                />
              </a>
            </div>
          </div>

          <div>
            <h4 class="font-marker text-lg text-white mb-6">Company</h4>
            <ul class="space-y-3">
              <li>
                <a
                  href="about.html"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >About Us</a
                >
              </li>
              <li>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Careers</a
                >
              </li>
              <li>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Contact Us</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-marker text-lg text-white mb-6">Community</h4>
            <ul class="space-y-3">
              <li>
                <a
                  href="https://partner.pyxi.ai/auth/register/"
                  target="_blank"
                  rel="external noopener noreferrer"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Partner with Us</a
                >
              </li>
              <li>
                <a
                  href="https://partner.pyxi.ai/auth/login/"
                  target="_blank"
                  rel="external noopener noreferrer"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Existing Partner</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-marker text-lg text-white mb-6">Legal & Social</h4>
            <ul class="space-y-3 mb-6">
              <li>
                <a
                  href="privacy-policy.html"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Privacy Policy</a
                >
              </li>
              <li>
                <a
                  href="terms-and-conditions.html"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Terms & Conditions</a
                >
              </li>
              <li>
                <a
                  href="community-guidelines.html"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Community guidelines</a
                >
              </li>
              <li>
                <a
                  href="mailto:<EMAIL>?subject=Reporting%20inappropriate%20behaviour"
                  class="text-gray-300 hover:text-pyxi-red transition-colors"
                  >Report a guest</a
                >
              </li>
            </ul>
            <div class="flex space-x-3">
              <a
                href="https://www.instagram.com/pyxi.ai/"
                class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-pyxi-red transition-colors group"
              >
                <svg
                  class="w-5 h-5 text-gray-300 group-hover:text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div
          class="border-t border-gray-800 mt-12 pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0"
        >
          <p class="text-gray-400 text-sm">
            &copy; 2024 Pyxi Technologies Ltd. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </body>
  <script>
    // Returning Diner Dates Logic
    function getNextNDates(weekday, n) {
      const dates = [];
      let date = new Date();
      // Set to tomorrow to avoid today if today is the day
      date.setDate(date.getDate() + 1);
      while (dates.length < n) {
        if (date.getDay() === weekday) {
          dates.push(new Date(date));
        }
        date.setDate(date.getDate() + 1);
      }
      return dates;
    }

    document
      .getElementById("returning-diner-form")
      .addEventListener("submit", async function (e) {
        e.preventDefault();
        const emailInput = document.getElementById("returning-diner-email");
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        // Show loader and disable button
        submitBtn.disabled = true;
        submitBtn.innerHTML =
          '<svg class="animate-spin h-5 w-5 mr-2 inline-block text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>Loading...';

        // Remove any previous error
        let errorMsg = document.getElementById("diner-error-msg");
        if (errorMsg) errorMsg.remove();

        try {
          // Check if PyxiConfig is available
          if (typeof PyxiConfig === 'undefined') {
            console.error('PyxiConfig is not defined. Make sure config.js is loaded properly.');
            let error = document.createElement("div");
            error.id = "diner-error-msg";
            error.className = "text-red-500 mt-4 text-sm text-center";
            error.textContent = "Configuration error. Please refresh the page and try again.";
            this.appendChild(error);
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
            return;
          }

          const email = encodeURIComponent(emailInput.value.trim());
          const response = await fetch(
            `${PyxiConfig.API.BASE_URL}${PyxiConfig.ENDPOINTS.CHECK_EMAIL_EXISTS}?email=${email}`,
            {
              method: "GET",
              headers: { accept: "application/json" },
            }
          );
          if (response.status === 200) {
            const data = await response.json();
            if (data.exists) {
              // Hide form
              this.classList.add("hidden");
              // Show dates
              const dinerDatesDiv = document.getElementById("diner-dates");
              const list = document.getElementById("diner-dates-list");
              list.innerHTML = "";
              // Get next 3 Tuesdays (2) and Thursdays (4)
              const tuesdays = getNextNDates(2, 3); // 2 = Tuesday
              const thursdays = getNextNDates(4, 3); // 4 = Thursday
              // Combine and label
              const allDates = [];
              for (let i = 0; i < 3; i++) {
                if (tuesdays[i])
                  allDates.push({ date: tuesdays[i], label: "Soho" });
                if (thursdays[i])
                  allDates.push({ date: thursdays[i], label: "Canary Wharf" });
              }
              // Sort by date ascending
              allDates.sort((a, b) => a.date - b.date);
              // Render
              allDates.forEach(({ date, label }) => {
                const displayStr = date.toLocaleDateString(undefined, {
                  weekday: "long",
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                });
                const dateParam = `${date.getFullYear()}-${String(
                  date.getMonth() + 1
                ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
                list.innerHTML += `<div class='diner-date-option group bg-gray-800 rounded-full px-6 py-3 font-semibold text-white shadow hover:bg-pyxi-red hover:text-white transition-all flex items-center gap-2 text-base w-[90%] whitespace-nowrap truncate mx-auto cursor-pointer' data-date="${dateParam}">
                <span class='text-pyxi-red font-bold truncate group-hover:text-white'>${displayStr}</span>
                <span class='text-gray-300 truncate group-hover:text-white'>— ${label}</span>
              </div>`;
              });
              // Add click event listeners to date options
              setTimeout(() => {
                const email = emailInput.value.trim();
                document
                  .querySelectorAll(".diner-date-option")
                  .forEach((option) => {
                    option.addEventListener("click", function () {
                      const date = this.getAttribute("data-date");
                      window.open(
                        `https://signup.pyxi.ai/ticket-date-book?email=${email}&date=${date}`,
                        "_blank"
                      );
                    });
                  });
              }, 0);
              dinerDatesDiv.classList.remove("hidden");
            } else {
              // Should not happen, but fallback error
              let error = document.createElement("div");
              error.id = "diner-error-msg";
              error.className = "text-red-500 mt-4";
              error.textContent = "Email not found. Please try again.";
              this.parentNode.appendChild(error);
              submitBtn.disabled = false;
              submitBtn.innerHTML = originalBtnText;
            }
          } else if (response.status === 404) {
            // Show error message
            let error = document.createElement("div");
            error.id = "diner-error-msg";
            error.className = "text-red-500 mt-4";
            error.textContent = "Email not found. Please try again.";
            this.parentNode.appendChild(error);
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
          } else {
            // Show error message
            let error = document.createElement("div");
            error.id = "diner-error-msg";
            error.className = "text-red-500 mt-4";
            error.textContent = "There was a problem. Please try again.";
            this.parentNode.appendChild(error);
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
          }
        } catch (err) {
          // Show error message
          let error = document.createElement("div");
          error.id = "diner-error-msg";
          error.className = "text-red-500 mt-4";
          error.textContent = "There was a problem. Please try again.";
          this.parentNode.appendChild(error);
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalBtnText;
        }
      });

    // Smooth scroll to returning-diner section
    function scrollToReturningDiner() {
      const section = document.getElementById("returning-diner");
      if (section) {
        section.scrollIntoView({ behavior: "smooth" });
      }
    }

    // Close mobile menu
    function closeMobileMenu() {
      const mobileMenu = document.getElementById("mobile-menu");
      if (mobileMenu) {
        mobileMenu.classList.add("hidden");
      }
    }
  </script>
</html>
